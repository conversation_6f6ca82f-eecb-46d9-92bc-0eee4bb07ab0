import { auth } from "@/libs/auth";
import { prisma } from "@/libs/prisma";
import { NextRequest, NextResponse } from "next/server";

export async function POST(req: NextRequest) {
  const session = await auth();
  const userRole = session?.user.systemRole;

  if (userRole !== "ADMIN" && userRole !== "DEVELOPER") {
    return NextResponse.json({ message: "沒有權限" }, { status: 403 });
  }

  try {
    const body = await req.json();
    const { ip, reason } = body;

    if (!ip) {
      return NextResponse.json({ message: "IP 地址不能為空" }, { status: 400 });
    }

    // Check if IP is already banned
    const existingBan = await prisma.banIp.findUnique({
      where: { ip },
    });

    if (existingBan) {
      return NextResponse.json({ message: "此 IP 已被封鎖" }, { status: 400 });
    }

    // Ban the IP
    await prisma.banIp.create({
      data: {
        ip,
      },
    });

    // Log the ban action
    console.log("IP banned:", {
      ip,
      reason,
      bannedBy: session?.user.id,
      timestamp: new Date().toISOString(),
    });

    return NextResponse.json({
      message: `IP ${ip} 已成功封鎖`,
      ip,
    });
  } catch (error) {
    console.error("Error banning IP:", error);
    return NextResponse.json({ message: "伺服器錯誤" }, { status: 500 });
  }
}

export async function DELETE(req: NextRequest) {
  const session = await auth();
  const userRole = session?.user.systemRole;

  if (userRole !== "ADMIN" && userRole !== "DEVELOPER") {
    return NextResponse.json({ message: "沒有權限" }, { status: 403 });
  }

  try {
    const { searchParams } = new URL(req.url);
    const ip = searchParams.get("ip");

    if (!ip) {
      return NextResponse.json({ message: "IP 地址不能為空" }, { status: 400 });
    }

    // Check if IP is banned
    const existingBan = await prisma.banIp.findUnique({
      where: { ip },
    });

    if (!existingBan) {
      return NextResponse.json({ message: "此 IP 未被封鎖" }, { status: 400 });
    }

    // Unban the IP
    await prisma.banIp.delete({
      where: { ip },
    });

    // Log the unban action
    console.log("IP unbanned:", {
      ip,
      unbannedBy: session?.user.id,
      timestamp: new Date().toISOString(),
    });

    return NextResponse.json({
      message: `IP ${ip} 已解除封鎖`,
      ip,
    });
  } catch (error) {
    console.error("Error unbanning IP:", error);
    return NextResponse.json({ message: "伺服器錯誤" }, { status: 500 });
  }
}

export async function GET(req: NextRequest) {
  const session = await auth();
  const userRole = session?.user.systemRole;

  if (userRole !== "ADMIN" && userRole !== "DEVELOPER") {
    return NextResponse.json({ message: "沒有權限" }, { status: 403 });
  }

  try {
    const bannedIps = await prisma.banIp.findMany({
      orderBy: {
        id: "desc",
      },
    });

    return NextResponse.json(bannedIps);
  } catch (error) {
    console.error("Error fetching banned IPs:", error);
    return NextResponse.json({ message: "伺服器錯誤" }, { status: 500 });
  }
}
