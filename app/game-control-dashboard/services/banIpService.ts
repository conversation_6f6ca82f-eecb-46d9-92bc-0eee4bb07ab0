interface BanIpRequest {
  ip: string;
  reason?: string;
}

interface BanIpResponse {
  message: string;
  ip: string;
}

interface BannedIp {
  id: string;
  ip: string;
}

export const banIpService = {
  // 封鎖 IP
  banIp: async (data: BanIpRequest): Promise<BanIpResponse> => {
    const response = await fetch("/api/admin/ban-ip", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || "封鎖 IP 失敗");
    }

    return response.json();
  },

  // 解除封鎖 IP
  unbanIp: async (ip: string): Promise<BanIpResponse> => {
    const response = await fetch(`/api/admin/ban-ip?ip=${encodeURIComponent(ip)}`, {
      method: "DELETE",
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || "解除封鎖 IP 失敗");
    }

    return response.json();
  },

  // 獲取已封鎖的 IP 列表
  getBannedIps: async (): Promise<BannedIp[]> => {
    const response = await fetch("/api/admin/ban-ip");

    if (!response.ok) {
      throw new Error("獲取封鎖 IP 列表失敗");
    }

    return response.json();
  },
};

export type { BanIpRequest, BanIpResponse, BannedIp };
