"use client";
import React, { useState } from "react";
import { Modal, Form, Input, Button, message } from "antd";
import { banIpService } from "../services/banIpService";

interface BanIpDialogProps {
  open: boolean;
  ip: string;
  onClose: () => void;
  onSuccess: () => void;
}

export const BanIpDialog: React.FC<BanIpDialogProps> = ({
  open,
  ip,
  onClose,
  onSuccess,
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  const handleSubmit = async (values: { reason?: string }) => {
    setLoading(true);
    try {
      await banIpService.banIp({
        ip,
        reason: values.reason,
      });

      message.success(`IP ${ip} 已成功封鎖`);
      form.resetFields();
      onSuccess();
      onClose();
    } catch (error) {
      message.error(error instanceof Error ? error.message : "封鎖失敗");
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    form.resetFields();
    onClose();
  };

  return (
    <Modal
      title="封鎖 IP 地址"
      open={open}
      onCancel={handleCancel}
      footer={null}
    >
      <div className="mb-4">
        <p className="text-gray-600">
          確定要封鎖以下 IP 地址嗎？
        </p>
        <p className="font-mono text-lg font-semibold text-red-600">
          {ip}
        </p>
      </div>

      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
      >
        <Form.Item
          name="reason"
          label="封鎖原因（選填）"
        >
          <Input.TextArea
            rows={3}
            placeholder="請輸入封鎖原因..."
            maxLength={200}
          />
        </Form.Item>

        <Form.Item className="mb-0">
          <div className="flex justify-end space-x-2">
            <Button onClick={handleCancel}>
              取消
            </Button>
            <Button
              type="primary"
              danger
              htmlType="submit"
              loading={loading}
            >
              確認封鎖
            </Button>
          </div>
        </Form.Item>
      </Form>
    </Modal>
  );
};
