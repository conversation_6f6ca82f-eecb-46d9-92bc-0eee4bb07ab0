"use client";
import React, { useState, useMemo, useEffect } from "react";
import {
  Table,
  DatePicker,
  Button,
  Space,
  Select,
  Tag,
  Input,
  message,
} from "antd";
import {
  DownloadOutlined,
  SearchOutlined,
  StopOutlined,
} from "@ant-design/icons";
import type { ColumnsType } from "antd/es/table";
import dayjs from "dayjs";
import { useDrawResults, useRefreshDrawResults } from "../hooks/useDrawResults";
import type { DrawRecord } from "../services/drawResultService";
import { GAME_NAMES, GameId } from "@/app/constants";
import { BanIpDialog } from "./BanIpDialog";

const { RangePicker } = DatePicker;
const { Option } = Select;
const { Search } = Input;

export const DrawResultPage: React.FC = () => {
  const [dateRange, setDateRange] = useState<[dayjs.Dayjs, dayjs.Dayjs]>([
    dayjs().startOf("day"),
    dayjs().endOf("day"),
  ]);
  const [gameFilter, setGameFilter] = useState<string>("all");
  const [resultFilter, setResultFilter] = useState<string>("all");
  const [searchText, setSearchText] = useState<string>("");
  const [debouncedSearchText, setDebouncedSearchText] = useState<string>("");
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);
  const [banIpDialogOpen, setBanIpDialogOpen] = useState(false);
  const [selectedIp, setSelectedIp] = useState<string>("");

  // Debounce search text
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchText(searchText);
    }, 300);
    return () => clearTimeout(timer);
  }, [searchText]);

  // React Query for fetching draw results
  const queryParams = useMemo(
    () => ({
      page: currentPage,
      pageSize,
      startDate: dateRange[0].startOf("day").toISOString(),
      endDate: dateRange[1].endOf("day").toISOString(),
      gameId: gameFilter === "all" ? undefined : gameFilter,
      result: resultFilter === "all" ? undefined : resultFilter,
      search: debouncedSearchText,
    }),
    [
      currentPage,
      pageSize,
      dateRange,
      gameFilter,
      resultFilter,
      debouncedSearchText,
    ],
  );

  const { data: drawResultsData, isLoading } = useDrawResults(queryParams);
  const refreshDrawResults = useRefreshDrawResults();

  const handleBanIp = (ip: string) => {
    if (!ip || ip === "unknown") {
      message.warning("無法封鎖未知 IP 地址");
      return;
    }
    setSelectedIp(ip);
    setBanIpDialogOpen(true);
  };

  const handleBanIpSuccess = () => {
    message.success("IP 封鎖成功");
    refreshDrawResults();
  };

  const columns: ColumnsType<DrawRecord> = [
    {
      title: "抽獎時間",
      dataIndex: "createdAt",
      key: "createdAt",
      render: (date: string) => dayjs(date).format("YYYY-MM-DD HH:mm:ss"),
      sorter: (a, b) => dayjs(a.createdAt).unix() - dayjs(b.createdAt).unix(),
    },
    {
      title: "用戶 ID",
      dataIndex: ["user", "id"],
      key: "id",
    },
    {
      title: "Email",
      dataIndex: ["user", "email"],
      key: "email",
      render: (email: string) => email || "未設定",
    },
    {
      title: "帳號名",
      dataIndex: ["user", "name"],
      key: "name",
    },
    {
      title: "暱稱",
      dataIndex: ["user", "nickname"],
      key: "nickname",
    },
    {
      title: "遊戲類型",
      dataIndex: "gameId",
      key: "gameId",
      render: (gameId: string) => {
        return GAME_NAMES[gameId as GameId] || gameId;
      },
    },
    {
      title: "遊戲分數",
      dataIndex: "score",
      key: "score",
      sorter: (a, b) => a.score - b.score,
    },
    {
      title: "抽獎結果",
      dataIndex: "result",
      key: "result",
      render: (result: string) => {
        switch (result) {
          case "win":
            return <Tag color="green">中獎</Tag>;
          case "lose":
            return <Tag color="red">未中獎</Tag>;
          default:
            return <Tag>無</Tag>;
        }
      },
    },
    {
      title: "PIN碼",
      dataIndex: ["coupon", "code"],
      key: "couponCode",
      render: (code: string) => code || "-",
    },
    {
      title: "IP 地址",
      dataIndex: "ip",
      key: "ip",
      render: (ip: string) => ip || "未記錄",
    },
    {
      title: "操作",
      key: "action",
      render: (_, record) => (
        <Button
          type="primary"
          danger
          size="small"
          icon={<StopOutlined />}
          onClick={() => handleBanIp(record.ip || "")}
          disabled={!record.ip || record.ip === "unknown"}
        >
          封鎖 IP
        </Button>
      ),
    },
  ];

  const exportToCSV = () => {
    if (!drawResultsData?.data) return;

    const csvContent = [
      [
        "抽獎時間",
        "用戶ID",
        "帳號名",
        "Email",
        "遊戲類型",
        "遊戲分數",
        "抽獎結果",
        "PIN碼",
        "IP地址",
      ],
      ...drawResultsData.data.map((record: DrawRecord) => [
        dayjs(record.createdAt).format("YYYY-MM-DD HH:mm:ss"),
        record.userId,
        record.user?.name,
        record.user?.email || "未設定",
        GAME_NAMES[record.gameId as GameId] || record.gameId,
        record.score.toString(),
        record.result === "win" ? "中獎" : "未中獎",
        record.coupon?.code || "-",
        record.ip || "未記錄",
      ]),
    ]
      .map((row) => row.join(","))
      .join("\n");

    const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
    const link = document.createElement("a");
    const url = URL.createObjectURL(blob);
    link.setAttribute("href", url);
    link.setAttribute(
      "download",
      `draw-records-${dateRange[0].format("YYYY-MM-DD")}-${dateRange[1].format("YYYY-MM-DD")}.csv`,
    );
    link.style.visibility = "hidden";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  return (
    <div className="h-full flex flex-col">
      <h2 className="text-xl font-bold mb-4">查詢 PIN 碼抽獎紀錄</h2>

      <Space className="mb-4" wrap>
        <RangePicker
          value={dateRange}
          onChange={(dates) =>
            dates && setDateRange(dates as [dayjs.Dayjs, dayjs.Dayjs])
          }
          format="YYYY-MM-DD"
        />

        <Select
          value={gameFilter}
          onChange={setGameFilter}
          style={{ width: 120 }}
          placeholder="遊戲類型"
        >
          <Option value="all">全部遊戲</Option>
          {Object.entries(GAME_NAMES).map(([gameId, gameName]) => (
            <Option key={gameId} value={gameId}>
              {gameName}
            </Option>
          ))}
        </Select>

        <Select
          value={resultFilter}
          onChange={setResultFilter}
          style={{ width: 120 }}
          placeholder="抽獎結果"
        >
          <Option value="all">全部結果</Option>
          <Option value="win">中獎</Option>
          <Option value="lose">未中獎</Option>
        </Select>

        <Search
          placeholder="搜索用戶名稱、Email 或 PIN碼"
          value={searchText}
          onChange={(e) => setSearchText(e.target.value)}
          style={{ width: 250 }}
          allowClear
        />

        <Button
          type="primary"
          icon={<SearchOutlined />}
          onClick={() => {
            refreshDrawResults();
            setCurrentPage(1);
          }}
          loading={isLoading}
        >
          查詢
        </Button>

        <Button
          icon={<DownloadOutlined />}
          onClick={exportToCSV}
          disabled={!drawResultsData?.data || drawResultsData.data.length === 0}
        >
          匯出CSV
        </Button>
      </Space>

      <Table
        columns={columns}
        dataSource={drawResultsData?.data || []}
        loading={isLoading}
        rowKey="id"
        scroll={{ x: 700 }}
        pagination={{
          current: currentPage,
          pageSize: pageSize,
          total: drawResultsData?.pagination.total || 0,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total) => `共 ${total} 筆記錄`,
          onChange: (page, size) => {
            setCurrentPage(page);
            if (size !== pageSize) {
              setPageSize(size);
              setCurrentPage(1);
            }
          },
          onShowSizeChange: (_, size) => {
            setPageSize(size);
            setCurrentPage(1);
          },
        }}
      />

      <BanIpDialog
        open={banIpDialogOpen}
        ip={selectedIp}
        onClose={() => setBanIpDialogOpen(false)}
        onSuccess={handleBanIpSuccess}
      />
    </div>
  );
};
